/* Custom CSS for Eldergrove Energy Website */

:root {
    --primary-color: #22c55e;
    --secondary-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --text-color: #374151;
    --text-light: #6b7280;
    --white: #ffffff;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
}

/* Header Styles */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--white);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.navbar-brand .brand-text {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-nav .nav-link {
    color: var(--text-color);
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
}

.cta-btn {
    background: var(--primary-color);
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.cta-btn:hover {
    background: #16a34a;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 5%;
    animation-delay: 2s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    padding-top: 100px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-blue {
    color: var(--secondary-color) !important;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    margin-bottom: 3rem;
}

.btn-primary {
    background: var(--primary-color);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #16a34a;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-image {
    position: relative;
    z-index: 2;
    text-align: center;
}

.image-placeholder {
    background: var(--white);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    color: var(--text-light);
    transition: transform 0.3s ease;
}

.image-placeholder:hover {
    transform: translateY(-10px);
}

.image-placeholder i {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Stats Section */
.stats-section {
    position: relative;
    z-index: 2;
    background: var(--white);
    border-radius: 20px;
    margin: 0 2rem;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    transform: translateY(-50px);
}

.stat-card {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-weight: 500;
}

/* Section Divider */
.section-divider {
    position: relative;
    z-index: 2;
    margin-top: -1px;
}

.section-divider svg {
    width: 100%;
    height: 120px;
    fill: var(--white);
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

/* About Section */
.about-section {
    padding: 5rem 0;
}

.about-content {
    padding-right: 2rem;
}

.features-list {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.feature-item i {
    margin-right: 1rem;
    font-size: 1.25rem;
}

/* Projects Section */
.projects-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.project-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.project-image {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.project-content {
    padding: 1.5rem;
}

.project-content h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
}

.contact-info {
    margin: 3rem 0;
}

.contact-item {
    text-align: center;
    padding: 1rem;
}

.contact-item h5 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.contact-form-container {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    margin-top: 3rem;
}

.contact-form .form-control {
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(34, 197, 94, 0.25);
}

.contact-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.contact-form button[type="submit"] {
    width: 100%;
    margin-top: 1rem;
}

/* Footer */
.footer {
    background: var(--dark-color) !important;
}

.footer h5, .footer h6 {
    color: var(--white);
    margin-bottom: 1rem;
}

.footer ul li {
    margin-bottom: 0.5rem;
}

.footer ul li a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer ul li a:hover {
    color: var(--primary-color);
}

.social-links a {
    color: var(--text-light);
    font-size: 1.5rem;
    margin-right: 1rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .stats-section {
        margin: 0 1rem;
        transform: translateY(-30px);
    }
    
    .about-content {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
